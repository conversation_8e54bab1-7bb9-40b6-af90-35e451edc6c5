#!/usr/bin/env python3
"""
Example of creating a custom metrics collector for specialized extraction workflows.
"""

from typing import Any, Dict, List
from extraction.metrics import BaseMetricsCollector, _c


class ProductExtractionMetrics(BaseMetricsCollector):
    """Custom metrics collector for product extraction tasks."""
    
    def __init__(self, name: str = "product_extraction"):
        super().__init__(name)
        self.target_features = ["price", "features", "name"]
    
    def collect_metrics(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Collect product-specific metrics."""
        if not results:
            return {"total_items": 0}
        
        # Basic metrics
        successful = [r for r in results if not r.get("error")]
        failed = [r for r in results if r.get("error")]
        
        # Product-specific metrics
        products_with_price = 0
        products_with_features = 0
        avg_feature_count = 0
        price_range = {"min": float('inf'), "max": 0}
        
        for result in successful:
            product = result.get("product", {})
            if isinstance(product, dict):
                # Check for price
                if product.get("price"):
                    products_with_price += 1
                    price = float(product["price"])
                    price_range["min"] = min(price_range["min"], price)
                    price_range["max"] = max(price_range["max"], price)
                
                # Check for features
                features = product.get("features", [])
                if features:
                    products_with_features += 1
                    avg_feature_count += len(features)
        
        if products_with_features > 0:
            avg_feature_count = avg_feature_count / products_with_features
        
        if price_range["min"] == float('inf'):
            price_range = {"min": 0, "max": 0}
        
        return {
            "extraction_name": self.name,
            "total_time": self.get_total_time(),
            "total_items": len(results),
            "successful_items": len(successful),
            "failed_items": len(failed),
            "success_rate": len(successful) / len(results) if results else 0.0,
            
            # Product-specific metrics
            "products_with_price": products_with_price,
            "products_with_features": products_with_features,
            "avg_feature_count": round(avg_feature_count, 2),
            "price_range": price_range,
            
            # Token metrics
            "total_input_tokens": sum(r.get("input_tokens", 0) for r in results),
            "total_output_tokens": sum(r.get("output_tokens", 0) for r in results),
            
            "errors": [r.get("error") for r in failed if r.get("error")]
        }
    
    def format_summary(self, metrics: Dict[str, Any]) -> str:
        """Format product extraction metrics."""
        lines = []
        lines.append(_c(f"=== {metrics['extraction_name'].title()} Metrics ===", "bold"))
        
        # Basic stats
        lines.append(f"Total products: {metrics['total_items']}")
        lines.append(f"Successfully extracted: {metrics['successful_items']} ({metrics['success_rate']:.1%})")
        
        if metrics['failed_items'] > 0:
            lines.append(_c(f"Failed: {metrics['failed_items']}", "yellow"))
        
        # Product-specific metrics
        if metrics.get('products_with_price', 0) > 0:
            lines.append(_c("Product Analysis:", "blue"))
            lines.append(f"  Products with price: {metrics['products_with_price']}")
            price_range = metrics.get('price_range', {})
            if price_range.get('max', 0) > 0:
                lines.append(f"  Price range: ${price_range['min']:.2f} - ${price_range['max']:.2f}")
        
        if metrics.get('products_with_features', 0) > 0:
            lines.append(f"  Products with features: {metrics['products_with_features']}")
            lines.append(f"  Avg features per product: {metrics.get('avg_feature_count', 0):.1f}")
        
        # Token usage
        total_tokens = metrics.get('total_input_tokens', 0) + metrics.get('total_output_tokens', 0)
        if total_tokens > 0:
            lines.append(_c("Token Usage:", "green"))
            lines.append(f"  Total: {total_tokens:,} tokens")
        
        # Timing
        if metrics.get('total_time', 0) > 0:
            lines.append(f"Processing time: {metrics['total_time']:.2f}s")
        
        return "\n".join(lines)


def main():
    """Example usage of custom metrics collector."""
    # Simulate some extraction results
    sample_results = [
        {
            "input_tokens": 100,
            "output_tokens": 200,
            "product": {
                "name": "Widget A",
                "price": 19.99,
                "features": ["wifi", "bluetooth"]
            }
        },
        {
            "input_tokens": 110,
            "output_tokens": 180,
            "product": {
                "name": "Gadget B", 
                "price": 49.99,
                "features": ["waterproof", "wireless charging", "5-year warranty"]
            }
        },
        {
            "input_tokens": 95,
            "output_tokens": 150,
            "error": "Failed to parse product information"
        }
    ]
    
    # Create custom metrics collector
    metrics_collector = ProductExtractionMetrics("custom_product_extraction")
    metrics_collector.start_timing()
    
    # Simulate some processing time
    import time
    time.sleep(0.1)
    
    metrics_collector.end_timing()
    
    # Collect and display metrics
    metrics = metrics_collector.collect_metrics(sample_results)
    print(metrics_collector.format_summary(metrics))
    
    # Save metrics
    from extraction.metrics import save_metrics_json
    save_metrics_json(metrics, "custom_metrics_example.json")
    print(f"\nMetrics saved to custom_metrics_example.json")


if __name__ == "__main__":
    main()
