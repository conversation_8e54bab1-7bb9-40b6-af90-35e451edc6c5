from __future__ import annotations

from typing import Any, Dict

from my_schema import ProductSpec, BasicEvaluation
from extraction.prompting import BasePromptBuilder

class ProductPrompt(BasePromptBuilder):
    def build(self, item: Dict[str, Any]) -> Dict[str, Any]:
        text = item["text"]
        return {
            "messages": [{"role": "user", "content": f"Extract ProductSpec as JSON from this text and adhere strictly to the schema.\n{text}"}],
            "response_format": ProductSpec,
            "result_key": "object",
        }
    
class EvaluationPrompt(BasePromptBuilder):
    def build(self, item: Dict[str, Any]) -> Dict[str, Any]:
        text = item
        extraction_output = item["extraction_output"]
        expected_format = item["expected_format"]
        prompt = f"""Evaluate the extraction result on two key criteria: 1. Is the extracted information factually accurate based on the input text? 2. Does the output follow the expected format/schema properly? Respond with your evaluation in the specified JSON format.

        **Original Input Text:**
        {text}

        **Extraction Output:**
        {extraction_output}

        **Expected Format:**
        {expected_format}
        """



        return {
            "messages": [{"role": "user", "content": f""}],
            "response_format": BasicEvaluation,
            "result_key": "object",
        }
