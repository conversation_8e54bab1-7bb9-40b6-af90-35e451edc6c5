from __future__ import annotations

from extraction.inference import get_language_model
from .my_prompt import ProductPrompt, EvaluationPrompt
import os

def format_evaluation_record(record):
    input_prompt = record[0]["messages"][0]["content"]
    expected_format = record[0]["response_format"].model_json_schema()
    extraction_output = record[1]["product"]

    return {
        "input_prompt": input_prompt,
        "expected_format": expected_format,
        "extraction_output": extraction_output
    }


api_key = os.getenv("OPENAI_API_KEY")

items = [
    {"text": "Widget X costs $19.99 and includes wifi, gps."},
    {"text": "Gadget Y is priced at $49.50, features: bluetooth, waterproofing"},
]

inputs = [ProductPrompt().build(x) for x in items]

lm = get_language_model(provider="openai")
results, _ = lm.generate(inputs)

for i, r in enumerate(results):
    print(i, r.get("product"))

formatted_records = [EvaluationPrompt().build(format_evaluation_record(r))for r in zip(inputs, results)]

evaluation_results, _ = lm.generate(formatted_records)

for i, r in enumerate(evaluation_results):
    print(i, r.get("evaluation"))