from __future__ import annotations

from extraction.inference import get_language_model
from .my_prompt import ProductPrompt

items = [
    {"text": "Widget X costs $19.99 and includes wifi, gps."},
    {"text": "Gadget Y is priced at $49.50, features: bluetooth, waterproofing"},
]

inputs = [ProductPrompt().build(x) for x in items]

lm = get_language_model(provider="openai")
results, _ = lm.generate(inputs)

for i, r in enumerate(results):
    print(i, r.get("product"))

