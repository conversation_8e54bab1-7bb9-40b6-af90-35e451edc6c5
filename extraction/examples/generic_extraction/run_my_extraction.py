from __future__ import annotations

from typing import Any, Dict, List, Optional, Tuple
import json
import os

from extraction.inference import get_language_model
from extraction import BaseLanguageModel, register_language_model
from .my_prompt import ProductPrompt, EvaluationPrompt


class CustomLanguageModel(BaseLanguageModel):
    """Custom language model that simulates realistic product extraction responses."""

    def __init__(self, *, model: Optional[str] = None, **kwargs) -> None:
        super().__init__(model=model or "custom-mock-model")
        # In a real implementation, you'd initialize your client here
        self.mock_responses = {
            "product": [
                {"name": "Widget X", "price": 19.99, "features": ["wifi", "gps"]},
                {"name": "Gadget Y", "price": 49.50, "features": ["bluetooth", "waterproofing"]},
                {"name": "Device Z", "price": 129.00, "features": ["premium materials", "AI processing"]}
            ],
            "evaluation": [
                {"correct": True, "formatted": True, "reasoning": "Extraction is accurate and properly formatted", "confidence": 0.95},
                {"correct": True, "formatted": True, "reasoning": "All required fields present and correct", "confidence": 0.90},
                {"correct": False, "formatted": True, "reasoning": "Price format incorrect", "confidence": 0.75}
            ]
        }
        self.call_count = 0

    def _generate_single(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a single response based on the payload."""
        result_key = payload.get("result_key", "result")

        # Simulate token usage
        input_tokens = len(str(payload.get("messages", []))) // 4  # Rough estimate
        output_tokens = 50  # Mock output tokens

        # Get mock response based on result_key
        if result_key in self.mock_responses:
            responses = self.mock_responses[result_key]
            response = responses[self.call_count % len(responses)]
            self.call_count += 1
        else:
            response = {"mock": True, "result_key": result_key}

        return {
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            result_key: response,
        }

    def generate(
        self,
        inputs: List[Dict[str, Any]],
        *,
        max_workers: Optional[int] = None,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """Generate responses for multiple inputs."""
        if not inputs:
            return [], 0

        results = []
        for item in inputs:
            try:
                results.append(self._generate_single(item))
            except Exception as e:
                result_key = item.get("result_key", "result")
                results.append({
                    "input_tokens": 0,
                    "output_tokens": 0,
                    result_key: None,
                    "error": str(e),
                })

        return results, len(inputs)


# Register the custom language model
register_language_model("custom", lambda **kw: CustomLanguageModel(**kw))


def format_evaluation_record(record):
    input_prompt = record[0]["messages"][0]["content"]
    expected_format = record[0]["response_format"].model_json_schema()
    extraction_output = record[1]["product"]

    return {
        "input_prompt": input_prompt,
        "expected_format": expected_format,
        "extraction_output": extraction_output
    }


api_key = os.getenv("OPENAI_API_KEY")

items = [
    {"text": "Widget X costs $19.99 and includes wifi, gps."},
    {"text": "Gadget Y is priced at $49.50, features: bluetooth, waterproofing"},
]

inputs = [ProductPrompt().build(x) for x in items]

lm = get_language_model(provider="openai")
results, _ = lm.generate(inputs)

for i, r in enumerate(results):
    print(i, r.get("product"))

formatted_records = [EvaluationPrompt().build(format_evaluation_record(r))for r in zip(inputs, results)]

evaluation_results, _ = lm.generate(formatted_records)

for i, r in enumerate(evaluation_results):
    print(i, r.get("evaluation"))