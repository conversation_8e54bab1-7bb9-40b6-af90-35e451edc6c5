from __future__ import annotations

# Fix the import path for Jupyter notebook
import sys
import os

# Add the project root to Python path
# Assuming notebook is in extraction/examples/generic_extraction/
project_root = os.path.abspath('../../../')
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from extraction.inference import get_language_model
from my_prompt import ProductPrompt

print('Imports successful!')

# Sample data
items = [
    {"text": "Widget X costs $19.99 and includes wifi, gps."},
    {"text": "Gadget Y is priced at $49.50, features: bluetooth, waterproofing"},
]

# Build prompts
inputs = [ProductPrompt().build(x) for x in items]
print(f"Built {len(inputs)} prompts")
inputs

# Get language model and run extraction

api_key = os.getenv("OPENAI_API_KEY")
lm = get_language_model(provider="openai", api_key=api_key)
results, _ = lm.generate(inputs)
print(f"Generated {len(results)} results")

results


import my_schema
records = [({'messages': [{'role': 'user',
     'content': 'Extract ProductSpec as JSON from this text and adhere strictly to the schema.\nWidget X costs $19.99 and includes wifi, gps.'}],
   'response_format': my_schema.ProductSpec,
   'result_key': 'product'},
  {'input_tokens': 98,
   'output_tokens': 416,
   'object': {'name': 'Widget X',
    'price': 19.99,
    'features': ['wifi', 'gps']}}),
 ({'messages': [{'role': 'user',
     'content': 'Extract ProductSpec as JSON from this text and adhere strictly to the schema.\nGadget Y is priced at $49.50, features: bluetooth, waterproofing'}],
   'response_format': my_schema.ProductSpec,
   'result_key': 'product'},
  {'input_tokens': 102,
   'output_tokens': 420,
   'object': {'name': 'Gadget Y',
    'price': 49.5,
    'features': ['bluetooth', 'waterproofing']}})]

# # Sample data
# items = [
#     {"text": "Widget X costs $19.99 and includes wifi, gps."},
#     {"text": "Gadget Y is priced at $49.50, features: bluetooth, waterproofing"},
# ]

# inputs = [ProductPrompt().build(x) for x in items]

# [i for i in zip(inputs, results)]




# records[0][1]["object"]
records[0][0]["messages"][0]["content"]


records[0][0]["response_format"]



for i, r in enumerate(results):
    print(i, r.get("product"))