from __future__ import annotations

# Fix the import path - go up to the root directory first
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from extraction.inference import get_language_model
from my_prompt import ProductPrompt

# Sample data
items = [
    {"text": "Widget X costs $19.99 and includes wifi, gps."},
    {"text": "Gadget Y is priced at $49.50, features: bluetooth, waterproofing"},
]

# Build prompts
inputs = [ProductPrompt().build(x) for x in items]
print(f"Built {len(inputs)} prompts")

# Get language model and run extraction
lm = get_language_model(provider="openai")
results, _ = lm.generate(inputs)
print(f"Generated {len(results)} results")

# Show the correspondence between inputs and results
print("=== Input-Result Correspondence ===")
for i, (inp, res) in enumerate(zip(inputs, results)):
    print(f"Item {i}:")
    print(f"  Input text: {items[i]['text']}")
    print(f"  Extracted: {res.get('product')}")
    if res.get('error'):
        print(f"  Error: {res.get('error')}")
    print()

# Simple results display
print("=== Simple Results ===")
for i, r in enumerate(results):
    print(i, r.get("product"))