from __future__ import annotations

# Fix the import path for Jupyter notebook
import sys
import os

# Add the project root to Python path
# Assuming notebook is in extraction/examples/generic_extraction/
project_root = os.path.abspath('../../../')
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from extraction.inference import get_language_model
from my_prompt import ProductPrompt

# Sample data
items = [
    {"text": "Widget X costs $19.99 and includes wifi, gps."},
    {"text": "Gadget Y is priced at $49.50, features: bluetooth, waterproofing"},
]

# Build prompts
inputs = [ProductPrompt().build(x) for x in items]
print(f"Built {len(inputs)} prompts")

# Get language model and run extraction
lm = get_language_model(provider="openai")
results, _ = lm.generate(inputs)
print(f"Generated {len(results)} results")

# Show the correspondence between inputs and results
print("=== Input-Result Correspondence ===")
for i, (inp, res) in enumerate(zip(inputs, results)):
    print(f"Item {i}:")
    print(f"  Input text: {items[i]['text']}")
    print(f"  Extracted: {res.get('product')}")
    if res.get('error'):
        print(f"  Error: {res.get('error')}")
    print()

# Simple results display
print("=== Simple Results ===")
for i, r in enumerate(results):
    print(i, r.get("product"))