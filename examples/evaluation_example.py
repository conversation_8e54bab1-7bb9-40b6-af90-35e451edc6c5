#!/usr/bin/env python3
"""
Example of using LLM-based evaluation to assess extraction quality.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from extraction.tasks.evaluation import (
    ProductEvaluationPromptBuilder,
    GenericEvaluationPromptBuilder,
    prepare_evaluation_items_from_data,
    prepare_evaluation_items_from_file,
    run_evaluation_extraction,
    compute_evaluation_summary
)
from extraction.inference import get_language_model


def example_with_sample_data():
    """Example using manually created sample data."""
    print("=== LLM-Based Evaluation Example ===\n")

    # Sample input texts and extraction outputs
    input_texts = [
        "Widget X costs $19.99 and includes wifi, gps, and bluetooth connectivity.",
        "Gadget Y is priced at $49.50 and features waterproofing, wireless charging, and a 5-year warranty.",
        "Device Z sells for $129.00 with premium materials, AI processing, and cloud sync capabilities."
    ]

    extraction_outputs = [
        {
            "name": "Widget X",
            "price": 19.99,
            "features": ["wifi", "gps", "bluetooth connectivity"]
        },
        {
            "name": "Gadget Y",
            "price": "forty-nine fifty",  # Incorrect format
            "features": ["waterproofing", "wireless charging", "5-year warranty"]
        },
        None  # LLM failed to extract
    ]

    # Prepare evaluation items
    items = prepare_evaluation_items_from_data(
        input_texts=input_texts,
        extraction_outputs=extraction_outputs,
        expected_format="JSON object with fields: name (string), price (number), features (array of strings)"
    )

    print(f"Prepared {len(items)} items for evaluation")

    # Create evaluation prompt builder
    prompt_builder = ProductEvaluationPromptBuilder()

    # Get language model (you'll need OPENAI_API_KEY set)
    try:
        lm = get_language_model(provider="openai")
        print("Using OpenAI for evaluation")
    except Exception as e:
        print(f"Could not initialize OpenAI: {e}")
        print("Trying Ollama instead...")
        try:
            lm = get_language_model(provider="ollama")
            print("Using Ollama for evaluation")
        except Exception as e2:
            print(f"Could not initialize Ollama: {e2}")
            print("Skipping LLM evaluation - no available provider")
            return

    # Run evaluation
    print("\nRunning LLM-based evaluation...")
    evaluation_results = run_evaluation_extraction(
        items=items,
        prompt_builder=prompt_builder,
        language_model=lm,
        max_workers=2
    )

    # Compute summary
    summary = compute_evaluation_summary(evaluation_results)

    print(f"\n=== Evaluation Results ===")
    print(f"Total items: {summary['total']}")
    print(f"Successfully evaluated: {summary['evaluated_items']}")
    print(f"Evaluation failures: {summary['evaluation_failures']}")
    print(f"Correct rate: {summary['correct_rate']:.1%}")
    print(f"Formatted rate: {summary['formatted_rate']:.1%}")
    print(f"Both correct & formatted: {summary['both_correct_rate']:.1%}")
    if summary.get('avg_confidence'):
        print(f"Average confidence: {summary['avg_confidence']:.3f}")
    print(f"Token usage: {summary['total_tokens']} total")

    # Show detailed results
    print(f"\n=== Detailed Results ===")
    for result in evaluation_results:
        eval_data = result.get("evaluation", {})
        if result.get("evaluation_success"):
            correct = "✓" if eval_data.get("correct") else "✗"
            formatted = "✓" if eval_data.get("formatted") else "✗"
            confidence = eval_data.get("confidence", "N/A")
            reasoning = eval_data.get("reasoning", "No reasoning provided")
            print(f"Item {result['id']}: Correct={correct}, Formatted={formatted}, Confidence={confidence}")
            print(f"  Reasoning: {reasoning}")
        else:
            print(f"Item {result['id']}: Evaluation failed - {result.get('evaluation_error', 'Unknown error')}")

    return evaluation_results


def example_with_evaluation_file():
    """Example using evaluation data file from --save-evaluation."""
    print("\n=== Example with Evaluation File ===")

    # This would typically be a file created by:
    # python -m extraction.main extract data.json my_prompts MyPrompt --save-evaluation
    evaluation_file = "sample_evaluation.json"

    # Create sample evaluation file for demo
    sample_data = [
        {
            "id": 0,
            "input": {
                "messages": [{"role": "user", "content": "Extract product info: Widget X costs $19.99 with wifi and bluetooth"}],
                "response_format": "ProductSpec",
                "result_key": "product"
            },
            "output": {
                "name": "Widget X",
                "price": 19.99,
                "features": ["wifi", "bluetooth"]
            },
            "success": True,
            "error": None
        },
        {
            "id": 1,
            "input": {
                "messages": [{"role": "user", "content": "Extract product info: Device Z sells for $129"}],
                "response_format": "ProductSpec",
                "result_key": "product"
            },
            "output": None,  # LLM failed
            "success": False,
            "error": "API timeout"
        }
    ]

    from extraction.io import write_json
    write_json(evaluation_file, sample_data)
    print(f"Created sample evaluation file: {evaluation_file}")

    try:
        # Load and prepare items
        items = prepare_evaluation_items_from_file(evaluation_file)
        print(f"Loaded {len(items)} items from evaluation file")

        # Create generic evaluator
        prompt_builder = GenericEvaluationPromptBuilder(
            task_name="product information extraction",
            schema_description="JSON object with name, price, and features fields"
        )

        print("Note: This example would run LLM evaluation if you have an API key configured")
        print("Items prepared for evaluation:")
        for item in items:
            print(f"  Item {item['id']}: {len(item['input_text'])} chars input, output={'present' if item['extraction_output'] else 'None'}")

    except Exception as e:
        print(f"Error in file-based example: {e}")

    finally:
        # Clean up
        try:
            import os
            os.remove(evaluation_file)
            print(f"Cleaned up {evaluation_file}")
        except:
            pass


def main():
    """Run evaluation examples."""
    # Run the sample data example
    example_with_sample_data()

    # Run the evaluation file example
    example_with_evaluation_file()

    print("\n=== Usage Summary ===")
    print("1. Run extraction with --save-evaluation:")
    print("   python -m extraction.main extract data.json my_prompts MyPrompt --save-evaluation")
    print("")
    print("2. Run LLM-based evaluation:")
    print("   python -m extraction.main extract data_evaluation.json extraction.tasks.evaluation.prompt ProductEvaluationPromptBuilder -o eval_results.json")
    print("")
    print("3. Or use the evaluation API directly as shown in this example")


if __name__ == "__main__":
    main()
